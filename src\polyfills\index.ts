// Polyfills for Node.js modules in browser environment
// This file ensures that Node.js core modules work properly in the browser

// Buffer polyfill - required for many crypto operations
import { B<PERSON><PERSON> } from 'buffer'

// Process polyfill - required for environment variables and process info
import process from 'process'

// Declare global types to avoid TypeScript errors
declare global {
  interface Window {
    Buffer: typeof import('buffer').Buffer
    process: typeof import('process')
    global: typeof globalThis
  }

  var Buffer: typeof import('buffer').Buffer
  var process: typeof import('process')
  var global: typeof globalThis
}

// Make Buffer and process available globally
if (typeof window !== 'undefined') {
  window.Buffer = Buffer
  window.process = process
  window.global = window
}

// Make them available globally for Node.js-style imports
globalThis.Buffer = Buffer
globalThis.process = process

// Set up process.env if it doesn't exist
if (!process.env) {
  process.env = {}
}

// Set NODE_ENV if not already set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development'
}

// Export for explicit imports if needed
export { Buffer, process }
